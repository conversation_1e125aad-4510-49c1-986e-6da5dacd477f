<!DOCTYPE html>
<html lang="en">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
      .slide-container {
        width: 1280px; 
        min-height: 720px; 
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        display: flex;
        align-items: center;
        position: relative;
        font-family: 'Inter', sans-serif;
        padding: 60px;
      }
      .content-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 80px;
        width: 100%;
        align-items: center;
      }
      .main-title {
        font-size: 48px;
        font-weight: 800;
        color: #ffffff;
        margin-bottom: 32px;
        line-height: 1.1;
      }
      .intro-text {
        font-size: 20px;
        color: #cbd5e1;
        line-height: 1.6;
        margin-bottom: 32px;
      }
      .magic-questions {
        space-y: 20px;
      }
      .question-item {
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.3);
        border-radius: 12px;
        padding: 20px;
        margin: 16px 0;
      }
      .question-title {
        font-size: 16px;
        font-weight: 700;
        color: #3b82f6;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
      }
      .question-text {
        font-size: 14px;
        color: #cbd5e1;
        line-height: 1.4;
        font-style: italic;
      }
      .visual-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .transformation-visual {
        background: rgba(249, 115, 22, 0.1);
        border: 2px solid rgba(249, 115, 22, 0.3);
        border-radius: 20px;
        padding: 32px;
        text-align: center;
        margin-bottom: 32px;
        width: 100%;
      }
      .before-after {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 16px;
        align-items: center;
        margin-bottom: 24px;
      }
      .before, .after {
        padding: 16px;
        border-radius: 12px;
        text-align: center;
      }
      .before {
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid rgba(239, 68, 68, 0.4);
      }
      .after {
        background: rgba(16, 185, 129, 0.2);
        border: 1px solid rgba(16, 185, 129, 0.4);
      }
      .before-title, .after-title {
        font-size: 14px;
        font-weight: 700;
        margin-bottom: 8px;
      }
      .before-title { color: #ef4444; }
      .after-title { color: #10b981; }
      .before-text, .after-text {
        font-size: 12px;
        line-height: 1.3;
      }
      .before-text { color: #fca5a5; }
      .after-text { color: #6ee7b7; }
      .arrow {
        font-size: 24px;
        color: #f97316;
      }
      .framework-box {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.3);
        border-radius: 12px;
        padding: 20px;
        text-align: center;
      }
      .framework-title {
        font-size: 16px;
        font-weight: 700;
        color: #10b981;
        margin-bottom: 8px;
      }
      .framework-text {
        font-size: 14px;
        color: #cbd5e1;
        line-height: 1.4;
      }
      .highlight {
        color: #f97316;
        font-weight: 700;
      }
      .emoji {
        font-size: 20px;
        margin-right: 8px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="content-grid">
        <div>
          <h1 class="main-title">Pillar 1: Ask Better Questions</h1>
          <p class="intro-text">
            The secret isn't building faster - it's building <span class="highlight">smarter</span>. 
            And that starts with asking the right questions before you write a single line of code.
          </p>
          
          <div class="magic-questions">
            <div class="question-item">
              <div class="question-title">
                <span class="emoji">🤔</span>
                The Magic Question #1
              </div>
              <div class="question-text">
                "What problem are we actually solving here?"
              </div>
            </div>
            
            <div class="question-item">
              <div class="question-title">
                <span class="emoji">👥</span>
                The Magic Question #2
              </div>
              <div class="question-text">
                "Who specifically has this problem, and how do we know?"
              </div>
            </div>
            
            <div class="question-item">
              <div class="question-title">
                <span class="emoji">📊</span>
                The Magic Question #3
              </div>
              <div class="question-text">
                "How will we know if we've actually solved it?"
              </div>
            </div>
            
            <div class="question-item">
              <div class="question-title">
                <span class="emoji">🚫</span>
                The Magic Question #4
              </div>
              <div class="question-text">
                "What happens if we don't build this at all?"
              </div>
            </div>
          </div>
        </div>
        
        <div class="visual-section">
          <div class="transformation-visual">
            <div class="before-after">
              <div class="before">
                <div class="before-title">❌ Old Way</div>
                <div class="before-text">"Build a dashboard with all the metrics"</div>
              </div>
              
              <div class="arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
              
              <div class="after">
                <div class="after-title">✅ New Way</div>
                <div class="after-text">"Help managers spot problems before they become crises"</div>
              </div>
            </div>
            
            <div class="before-after">
              <div class="before">
                <div class="before-title">❌ Old Way</div>
                <div class="before-text">"Add advanced search filters"</div>
              </div>
              
              <div class="arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
              
              <div class="after">
                <div class="after-title">✅ New Way</div>
                <div class="after-text">"Reduce time to find relevant info from 5 min to 30 sec"</div>
              </div>
            </div>
            
            <div class="before-after">
              <div class="before">
                <div class="before-title">❌ Old Way</div>
                <div class="before-text">"Build mobile app"</div>
              </div>
              
              <div class="arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
              
              <div class="after">
                <div class="after-title">✅ New Way</div>
                <div class="after-text">"Let field workers update status without returning to office"</div>
              </div>
            </div>
          </div>
          
          <div class="framework-box">
            <div class="framework-title">
              <span class="emoji">💡</span>
              The Simple Rule
            </div>
            <div class="framework-text">
              If you can't explain the problem in one sentence, 
              you're not ready to build the solution.
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

