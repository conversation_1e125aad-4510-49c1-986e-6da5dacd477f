<!DOCTYPE html>
<html lang="en">

<head>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://d3js.org/d3.v7.min.js"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  <style>
    .slide-container {
      width: 1280px;
      min-height: 720px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      font-family: 'Inter', sans-serif;
      padding: 60px;
    }

    .main-title {
      font-size: 48px;
      font-weight: 800;
      color: #1e293b;
      text-align: center;
      margin-bottom: 48px;
      line-height: 1.1;
    }

    .story-container {
      background: rgba(59, 130, 246, 0.15);
      border: 2px solid rgba(59, 130, 246, 0.3);
      border-radius: 24px;
      padding: 48px;
      margin-bottom: 32px;
    }

    .story-header {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
    }

    .story-icon {
      background: #3b82f6;
      color: white;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      font-size: 24px;
    }

    .story-title {
      font-size: 28px;
      font-weight: 700;
      color: #3b82f6;
    }

    .story-content {
      font-size: 18px;
      color: #475569;
      line-height: 1.6;
      margin-bottom: 24px;
    }

    .timeline {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
      margin-bottom: 32px;
    }

    .timeline-item {
      background: rgba(239, 68, 68, 0.1);
      border: 1px solid rgba(239, 68, 68, 0.3);
      border-radius: 12px;
      padding: 20px;
      text-align: center;
    }

    .timeline-step {
      background: #ef4444;
      color: white;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-weight: 700;
      font-size: 14px;
    }

    .timeline-title {
      font-size: 14px;
      font-weight: 700;
      color: #ef4444;
      margin-bottom: 8px;
    }

    .timeline-text {
      font-size: 12px;
      color: #475569;
      line-height: 1.3;
    }

    .lesson-learned {
      background: rgba(16, 185, 129, 0.1);
      border: 2px solid rgba(16, 185, 129, 0.3);
      border-radius: 16px;
      padding: 24px;
      text-align: center;
    }

    .lesson-title {
      font-size: 20px;
      font-weight: 700;
      color: #10b981;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .lesson-text {
      font-size: 16px;
      color: #475569;
      line-height: 1.5;
    }

    .highlight {
      color: #f97316;
      font-weight: 700;
    }

    .emoji {
      font-size: 20px;
      margin-right: 8px;
    }
  </style>
</head>

<body>
  <div class="slide-container">
    <h1 class="main-title">The Dashboard That Ate Everything</h1>

    <div class="story-container">
      <div class="story-header">
        <div class="story-icon">
          <i class="fas fa-chart-pie"></i>
        </div>
        <div class="story-title">A True Story from My Past</div>
      </div>

      <div class="story-content">
        Our sales team asked for "better reporting." Simple enough, right?
        Six months later, we had built a dashboard with <span class="highlight">47 different widgets</span>,
        12 chart types, and enough filters to make a NASA engineer weep.
      </div>

      <div class="timeline">
        <div class="timeline-item">
          <div class="timeline-step">1</div>
          <div class="timeline-title">The Request</div>
          <div class="timeline-text">"We need better sales reporting"</div>
        </div>

        <div class="timeline-item">
          <div class="timeline-step">2</div>
          <div class="timeline-title">The Assumption</div>
          <div class="timeline-text">"More data = better decisions"</div>
        </div>

        <div class="timeline-item">
          <div class="timeline-step">3</div>
          <div class="timeline-title">The Reality</div>
          <div class="timeline-text">"This is too complicated to use"</div>
        </div>
      </div>

      <div class="story-content">
        <strong>The plot twist?</strong> After talking to actual salespeople, we discovered they just wanted to know
        <span class="highlight">which leads were getting cold</span>. A simple email notification solved their real
        problem
        in 2 days, not 6 months.
      </div>
    </div>

    <div class="lesson-learned">
      <div class="lesson-title">
        <span class="emoji">💡</span>
        The Lesson
      </div>
      <div class="lesson-text">
        We spent 6 months building what we <em>thought</em> they needed,
        instead of 6 minutes asking what they <em>actually</em> needed.
      </div>
    </div>
  </div>
</body>

</html>